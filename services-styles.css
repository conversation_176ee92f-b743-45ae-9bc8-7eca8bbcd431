/* Services Page Specific Styles */

/* Page Header */
.page-header {
    min-height: 60vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
    margin-top: 80px;
}

.header-content {
    text-align: center;
    position: relative;
    z-index: 2;
}

.page-title {
    font-size: 4rem;
    font-weight: 700;
    color: white;
    margin-bottom: 1rem;
    animation: fadeInUp 1s ease-out;
}

.page-subtitle {
    font-size: 1.3rem;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 2rem;
    animation: fadeInUp 1s ease-out 0.2s both;
}

.breadcrumb {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    font-size: 1rem;
    animation: fadeInUp 1s ease-out 0.4s both;
}

.breadcrumb a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: color 0.3s ease;
}

.breadcrumb a:hover {
    color: white;
}

.breadcrumb span {
    color: rgba(255, 255, 255, 0.6);
}

/* Header Particles */
.header-particles {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
}

.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 50%;
    animation: particle-float 8s ease-in-out infinite;
}

.particle:nth-child(1) {
    top: 20%;
    left: 20%;
    animation-delay: 0s;
}

.particle:nth-child(2) {
    top: 60%;
    left: 80%;
    animation-delay: 2s;
}

.particle:nth-child(3) {
    top: 80%;
    left: 40%;
    animation-delay: 4s;
}

.particle:nth-child(4) {
    top: 30%;
    left: 70%;
    animation-delay: 6s;
}

.particle:nth-child(5) {
    top: 70%;
    left: 10%;
    animation-delay: 8s;
}

@keyframes particle-float {
    0%, 100% {
        transform: translateY(0px) scale(1);
        opacity: 0.6;
    }
    50% {
        transform: translateY(-20px) scale(1.2);
        opacity: 1;
    }
}

/* Services Section */
.services-section {
    padding: 5rem 0;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.service-card {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
    border: 1px solid rgba(102, 126, 234, 0.1);
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 50px rgba(102, 126, 234, 0.2);
}

.service-card.premium {
    border: 2px solid #f39c12;
    position: relative;
}

.service-card.featured {
    border: 2px solid #e74c3c;
    transform: scale(1.05);
}

.service-card.featured:hover {
    transform: scale(1.05) translateY(-10px);
}

.service-header {
    padding: 2rem;
    text-align: center;
    position: relative;
}

.service-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    transition: all 0.3s ease;
}

.service-card:hover .service-icon {
    transform: scale(1.1) rotate(5deg);
}

.service-icon i {
    font-size: 2rem;
    color: white;
}

.service-header h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.service-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    padding: 0.3rem 0.8rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.service-badge.premium {
    background: linear-gradient(45deg, #f39c12, #e67e22);
    color: white;
}

.service-badge.featured {
    background: linear-gradient(45deg, #e74c3c, #c0392b);
    color: white;
    animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.service-content {
    padding: 0 2rem 2rem;
}

.service-content p {
    color: #7f8c8d;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.service-features {
    list-style: none;
    margin-bottom: 2rem;
}

.service-features li {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    margin-bottom: 0.8rem;
    color: #2c3e50;
}

.service-features i {
    color: #27ae60;
    font-size: 0.9rem;
}

.service-price {
    text-align: center;
    margin-bottom: 1.5rem;
}

.price {
    font-size: 2rem;
    font-weight: 700;
    color: #667eea;
}

.period {
    color: #7f8c8d;
    font-size: 0.9rem;
    margin-left: 0.5rem;
}

.service-btn {
    width: 100%;
    padding: 1rem;
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border: none;
    border-radius: 50px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.service-btn:hover {
    background: linear-gradient(45deg, #764ba2, #667eea);
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
}

/* Process Section */
.process {
    padding: 5rem 0;
    background: white;
}

.process-timeline {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.process-step {
    text-align: center;
    position: relative;
}

.step-number {
    width: 60px;
    height: 60px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0 auto 1.5rem;
    position: relative;
    z-index: 2;
}

.step-content h4 {
    font-size: 1.3rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 1rem;
}

.step-content p {
    color: #7f8c8d;
    line-height: 1.6;
}

/* CTA Section */
.cta {
    padding: 5rem 0;
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
    text-align: center;
}

.cta-content h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 1rem;
}

.cta-content p {
    font-size: 1.2rem;
    color: #34495e;
    margin-bottom: 2rem;
}

.cta-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* Responsive Design */
@media (max-width: 768px) {
    .page-title {
        font-size: 2.5rem;
    }
    
    .services-grid {
        grid-template-columns: 1fr;
    }
    
    .service-card.featured {
        transform: none;
    }
    
    .service-card.featured:hover {
        transform: translateY(-10px);
    }
    
    .process-timeline {
        grid-template-columns: 1fr;
    }
    
    .cta-content h2 {
        font-size: 2rem;
    }
    
    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }
}

@media (max-width: 480px) {
    .page-title {
        font-size: 2rem;
    }
    
    .page-subtitle {
        font-size: 1.1rem;
    }
    
    .services-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .service-card {
        margin: 0 1rem;
    }
    
    .cta-content h2 {
        font-size: 1.8rem;
    }
}
