/* Portfolio Page Specific Styles */

/* Page Header */
.page-header {
    min-height: 60vh;
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
    margin-top: 80px;
}

.header-content {
    text-align: center;
    position: relative;
    z-index: 2;
}

.page-title {
    font-size: 4rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 1rem;
    animation: fadeInUp 1s ease-out;
}

.page-subtitle {
    font-size: 1.3rem;
    color: #34495e;
    margin-bottom: 2rem;
    animation: fadeInUp 1s ease-out 0.2s both;
}

.breadcrumb {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    font-size: 1rem;
    animation: fadeInUp 1s ease-out 0.4s both;
}

.breadcrumb a {
    color: #667eea;
    text-decoration: none;
    transition: color 0.3s ease;
}

.breadcrumb a:hover {
    color: #2c3e50;
}

.breadcrumb span {
    color: #7f8c8d;
}

/* Header Waves */
.header-waves {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    overflow: hidden;
    line-height: 0;
}

.wave {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100px;
    background: white;
    clip-path: polygon(0 20px, 100% 0, 100% 100%, 0 100%);
}

.wave1 {
    animation: wave-animation 3s ease-in-out infinite;
    opacity: 0.7;
}

.wave2 {
    animation: wave-animation 3s ease-in-out infinite reverse;
    opacity: 0.5;
    height: 80px;
}

.wave3 {
    animation: wave-animation 3s ease-in-out infinite;
    opacity: 0.3;
    height: 60px;
}

@keyframes wave-animation {
    0%, 100% {
        clip-path: polygon(0 20px, 100% 0, 100% 100%, 0 100%);
    }
    50% {
        clip-path: polygon(0 0, 100% 20px, 100% 100%, 0 100%);
    }
}

/* Portfolio Filter */
.portfolio-filter {
    padding: 3rem 0 1rem;
    background: white;
}

.filter-buttons {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.filter-btn {
    padding: 0.8rem 1.5rem;
    background: transparent;
    border: 2px solid #667eea;
    color: #667eea;
    border-radius: 50px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.filter-btn:hover,
.filter-btn.active {
    background: #667eea;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

/* Portfolio Section */
.portfolio-section {
    padding: 2rem 0 5rem;
    background: white;
}

.portfolio-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.portfolio-item {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    opacity: 1;
    transform: scale(1);
}

.portfolio-item.hide {
    opacity: 0;
    transform: scale(0.8);
    pointer-events: none;
}

.portfolio-item:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.portfolio-image {
    position: relative;
    height: 250px;
    overflow: hidden;
}

.image-placeholder {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.1rem;
    font-weight: 600;
}

.image-placeholder i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.8;
}

.portfolio-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(102, 126, 234, 0.95);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s ease;
}

.portfolio-item:hover .portfolio-overlay {
    opacity: 1;
}

.overlay-content {
    text-align: center;
    color: white;
    padding: 2rem;
}

.overlay-content h4 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.overlay-content p {
    margin-bottom: 1.5rem;
    opacity: 0.9;
}

.portfolio-links {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

.portfolio-link {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
    transition: all 0.3s ease;
}

.portfolio-link:hover {
    background: white;
    color: #667eea;
    transform: scale(1.1);
}

.portfolio-info {
    padding: 1.5rem;
}

.portfolio-info h4 {
    font-size: 1.2rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.portfolio-category {
    color: #667eea;
    font-size: 0.9rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Testimonials */
.testimonials {
    padding: 5rem 0;
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

.testimonials-slider {
    max-width: 800px;
    margin: 0 auto;
    position: relative;
}

.testimonial-item {
    display: none;
    text-align: center;
    padding: 2rem;
}

.testimonial-item.active {
    display: block;
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.testimonial-content {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    padding: 3rem;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.quote-icon {
    font-size: 3rem;
    color: #667eea;
    margin-bottom: 1.5rem;
}

.testimonial-content p {
    font-size: 1.2rem;
    line-height: 1.8;
    color: #2c3e50;
    margin-bottom: 2rem;
    font-style: italic;
}

.testimonial-author {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
}

.author-avatar {
    width: 60px;
    height: 60px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.author-info h5 {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.2rem;
}

.author-info span {
    color: #7f8c8d;
    font-size: 0.9rem;
}

.testimonial-dots {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-top: 2rem;
}

.dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(102, 126, 234, 0.3);
    cursor: pointer;
    transition: all 0.3s ease;
}

.dot.active,
.dot:hover {
    background: #667eea;
    transform: scale(1.2);
}

/* Responsive Design */
@media (max-width: 768px) {
    .page-title {
        font-size: 2.5rem;
    }
    
    .portfolio-grid {
        grid-template-columns: 1fr;
    }
    
    .filter-buttons {
        gap: 0.5rem;
    }
    
    .filter-btn {
        padding: 0.6rem 1rem;
        font-size: 0.9rem;
    }
    
    .testimonial-content {
        padding: 2rem;
    }
    
    .testimonial-author {
        flex-direction: column;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .page-title {
        font-size: 2rem;
    }
    
    .page-subtitle {
        font-size: 1.1rem;
    }
    
    .portfolio-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .portfolio-item {
        margin: 0 1rem;
    }
    
    .testimonial-content p {
        font-size: 1.1rem;
    }
}
