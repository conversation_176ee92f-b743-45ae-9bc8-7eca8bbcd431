/* About Page Specific Styles */

/* Page Header */
.page-header {
    min-height: 60vh;
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
    margin-top: 80px;
}

.header-content {
    text-align: center;
    position: relative;
    z-index: 2;
}

.page-title {
    font-size: 4rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 1rem;
    animation: fadeInUp 1s ease-out;
}

.page-subtitle {
    font-size: 1.3rem;
    color: #34495e;
    margin-bottom: 2rem;
    animation: fadeInUp 1s ease-out 0.2s both;
}

.breadcrumb {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    font-size: 1rem;
    animation: fadeInUp 1s ease-out 0.4s both;
}

.breadcrumb a {
    color: #667eea;
    text-decoration: none;
    transition: color 0.3s ease;
}

.breadcrumb a:hover {
    color: #2c3e50;
}

.breadcrumb span {
    color: #7f8c8d;
}

/* Header Decorations */
.header-decoration {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
}

.floating-shape {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    animation: float 6s ease-in-out infinite;
}

.shape-1 {
    width: 100px;
    height: 100px;
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.shape-2 {
    width: 150px;
    height: 150px;
    top: 60%;
    right: 15%;
    animation-delay: 2s;
}

.shape-3 {
    width: 80px;
    height: 80px;
    bottom: 20%;
    left: 70%;
    animation-delay: 4s;
}

/* About Content */
.about-content {
    padding: 5rem 0;
    background: white;
}

.about-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.about-text h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 1.5rem;
}

.lead {
    font-size: 1.2rem;
    color: #667eea;
    font-weight: 500;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.about-text p {
    color: #7f8c8d;
    line-height: 1.8;
    margin-bottom: 1.5rem;
}

.about-features {
    margin-top: 2rem;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
    padding: 0.5rem 0;
}

.feature-item i {
    color: #27ae60;
    font-size: 1.2rem;
}

.feature-item span {
    color: #2c3e50;
    font-weight: 500;
}

/* About Image */
.about-image {
    position: relative;
}

.image-container {
    position: relative;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.image-container:hover {
    transform: translateY(-10px);
    box-shadow: 0 30px 60px rgba(0, 0, 0, 0.15);
}

.image-placeholder {
    height: 400px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    font-weight: 600;
}

.image-placeholder i {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.8;
}

.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(102, 126, 234, 0.8), rgba(118, 75, 162, 0.8));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.image-container:hover .image-overlay {
    opacity: 1;
}

/* Values Section */
.values {
    padding: 5rem 0;
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.values .section-title {
    color: white;
    margin-bottom: 3rem;
}

.values .section-title::after {
    background: white;
}

.values-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.value-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 2.5rem;
    border-radius: 20px;
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.value-card:hover {
    transform: translateY(-10px);
    background: white;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.value-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(45deg, #f093fb, #f5576c);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    transition: all 0.3s ease;
}

.value-card:hover .value-icon {
    transform: scale(1.1) rotate(5deg);
}

.value-icon i {
    font-size: 2rem;
    color: white;
}

.value-card h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 1rem;
}

.value-card p {
    color: #7f8c8d;
    line-height: 1.6;
}

/* Team Section */
.team {
    padding: 5rem 0;
    background: white;
}

.team-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.team-member {
    text-align: center;
    transition: all 0.3s ease;
}

.team-member:hover {
    transform: translateY(-10px);
}

.member-image {
    position: relative;
    margin-bottom: 1.5rem;
    display: inline-block;
}

.avatar {
    width: 200px;
    height: 200px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 4rem;
    margin: 0 auto;
    transition: all 0.3s ease;
}

.member-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(102, 126, 234, 0.9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s ease;
}

.member-image:hover .member-overlay {
    opacity: 1;
}

.member-overlay .social-links {
    display: flex;
    gap: 1rem;
}

.member-overlay .social-links a {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
    transition: all 0.3s ease;
}

.member-overlay .social-links a:hover {
    background: white;
    color: #667eea;
    transform: scale(1.1);
}

.member-info h4 {
    font-size: 1.3rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.member-info p {
    color: #667eea;
    font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
    .page-title {
        font-size: 2.5rem;
    }
    
    .about-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .values-grid {
        grid-template-columns: 1fr;
    }
    
    .team-grid {
        grid-template-columns: 1fr;
    }
    
    .avatar {
        width: 150px;
        height: 150px;
        font-size: 3rem;
    }
}

@media (max-width: 480px) {
    .page-title {
        font-size: 2rem;
    }
    
    .page-subtitle {
        font-size: 1.1rem;
    }
    
    .about-text h2 {
        font-size: 2rem;
    }
}
